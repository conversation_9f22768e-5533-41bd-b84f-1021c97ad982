# 闲鱼商品自动发布脚本

基于 Playwright 的闲鱼商品自动发布工具，可以自动化完成商品信息填写、图片上传和发布流程。

## 功能特性

- ✅ 自动选择商品分类（3C数码/笔记本电脑）
- ✅ 自动填写商品属性（品牌、型号、成色等）
- ✅ 自动上传商品图片
- ✅ 自动填写商品标题和描述
- ✅ 自动设置价格和库存
- ✅ 自动发布商品
- ✅ 支持批量发布
- ✅ 错误处理和日志记录

## 安装依赖

```bash
# 安装 Node.js 依赖
npm install

# 安装 Playwright 浏览器
npm run install-playwright
```

## 使用方法

### 1. 单个商品发布

```bash
# 发布单个商品
node example-usage.js single
```

### 2. 批量商品发布

```bash
# 批量发布多个商品
node example-usage.js batch
```

### 3. 自定义使用

```javascript
const GoofishProductPublisher = require('./goofish-product-publisher');
const path = require('path');

async function customPublish() {
    const publisher = new GoofishProductPublisher();
    
    const productData = {
        title: '您的商品标题',
        description: '详细的商品描述...',
        price: 1000,
        stock: 1
    };
    
    const imagePath = path.join(__dirname, 'your-image.jpg');
    
    await publisher.publishProductFlow(productData, imagePath);
}

customPublish();
```

## 配置说明

### 商品数据结构

```javascript
const productData = {
    title: '商品标题（最多30个汉字）',
    description: '商品描述（最多5000字符）',
    price: 价格（数字，单位：元）,
    stock: 库存数量（数字）
};
```

### 图片要求

- 支持格式：PNG、JPG、JPEG
- 文件大小：≤ 10MB
- 建议比例：4:3、1:1、3:4
- 最多上传9张图片

## 自动化流程

脚本会按以下步骤自动执行：

1. **打开发布页面** - 访问闲鱼商品发布页面
2. **选择分类** - 自动选择"3C数码 > 笔记本电脑"
3. **填写属性** - 选择品牌、型号、成色等属性
4. **上传图片** - 上传指定的商品图片
5. **填写信息** - 输入标题、描述、发货地
6. **设置价格** - 设置售价和库存
7. **发布商品** - 点击确定完成发布

## 注意事项

### 使用前准备

1. 确保已登录闲鱼商家后台
2. 准备好商品图片文件
3. 确认网络连接稳定

### 安全建议

- 不要频繁发布，建议商品间隔30秒以上
- 确保商品信息真实准确
- 遵守闲鱼平台规则和政策
- 建议在测试环境先验证脚本功能

### 错误处理

脚本包含完整的错误处理机制：

- 网络超时自动重试
- 元素定位失败提示
- 发布状态检查
- 详细的日志输出

## 文件结构

```
├── goofish-product-publisher.js  # 主要发布类
├── example-usage.js              # 使用示例
├── package.json                  # 项目配置
├── README.md                     # 说明文档
└── apple.jpg                     # 示例图片
```

## 自定义扩展

### 添加新的商品分类

修改 `selectCategory()` 方法中的分类选择逻辑：

```javascript
async selectCategory(category) {
    // 根据传入的分类参数选择不同分类
    await this.page.click(`text=${category.main}`);
    await this.page.click(`text=${category.sub}`);
    // ...
}
```

### 添加更多商品属性

在 `fillProductAttributes()` 方法中添加新的属性选择：

```javascript
async fillProductAttributes(attributes) {
    // 根据传入的属性对象填写各种属性
    if (attributes.brand) {
        await this.page.click(`text=${attributes.brand}`);
    }
    // ...
}
```

## 故障排除

### 常见问题

1. **浏览器启动失败**
   ```bash
   npm run install-playwright
   ```

2. **元素定位失败**
   - 检查页面是否完全加载
   - 确认页面结构是否有变化

3. **图片上传失败**
   - 检查图片文件是否存在
   - 确认图片格式和大小符合要求

4. **发布失败**
   - 检查网络连接
   - 确认账号登录状态
   - 验证商品信息完整性

## 版本历史

- v1.0.0 - 初始版本，支持基本的商品发布功能

## 许可证

MIT License

## 贡献

欢迎提交 Issue 和 Pull Request 来改进这个项目。
