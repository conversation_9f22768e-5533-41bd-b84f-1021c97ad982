/**
 * 闲鱼商品发布脚本使用示例
 */

const GoofishProductPublisher = require('./goofish-product-publisher');
const path = require('path');

// 商品配置示例
const productConfigs = [
    {
        title: '苹果MacBook Pro 2023 14寸 16GB 几乎全新',
        description: '苹果MacBook Pro 2023款 14寸笔记本电脑，配置16GB内存，几乎全新成色，中国大陆购买，无任何拆修记录。外观精美，性能强劲，适合办公、设计、编程等多种用途。原装配件齐全，支持包邮发货。',
        price: 12800,
        stock: 1,
        imagePath: path.join(__dirname, 'apple.jpg')
    },
    {
        title: '苹果iPhone 15 Pro 256GB 深空黑色',
        description: '全新未拆封苹果iPhone 15 Pro，256GB存储容量，深空黑色，国行正品，支持全国联保。配备A17 Pro芯片，钛金属设计，支持USB-C接口。原装配件齐全，包装完整。',
        price: 8999,
        stock: 2,
        imagePath: path.join(__dirname, 'iphone15.jpg')
    }
];

async function publishMultipleProducts() {
    console.log('🚀 开始批量发布商品...');
    
    for (let i = 0; i < productConfigs.length; i++) {
        const config = productConfigs[i];
        console.log(`\n📦 正在发布第 ${i + 1} 个商品: ${config.title}`);
        
        const publisher = new GoofishProductPublisher();
        
        try {
            await publisher.publishProductFlow(config, config.imagePath);
            console.log(`✅ 第 ${i + 1} 个商品发布完成`);
            
            // 商品之间间隔30秒，避免频繁操作
            if (i < productConfigs.length - 1) {
                console.log('⏳ 等待30秒后发布下一个商品...');
                await new Promise(resolve => setTimeout(resolve, 30000));
            }
            
        } catch (error) {
            console.error(`❌ 第 ${i + 1} 个商品发布失败:`, error);
        }
    }
    
    console.log('\n🎉 批量发布任务完成！');
}

async function publishSingleProduct() {
    console.log('🚀 开始发布单个商品...');
    
    const publisher = new GoofishProductPublisher();
    const config = productConfigs[0]; // 使用第一个配置
    
    await publisher.publishProductFlow(config, config.imagePath);
}

// 根据命令行参数选择执行模式
const mode = process.argv[2] || 'single';

if (mode === 'batch') {
    publishMultipleProducts().catch(console.error);
} else {
    publishSingleProduct().catch(console.error);
}

// 导出配置供其他文件使用
module.exports = {
    productConfigs,
    publishMultipleProducts,
    publishSingleProduct
};
